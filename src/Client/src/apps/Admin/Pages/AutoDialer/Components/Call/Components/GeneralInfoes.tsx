import {
  MailOutlined,
  PhoneOutlined,
  ShopOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import {
  Col,
  Row,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import UploadImgFile from "@/apps/Admin/Pages/Users/<USER>/AddOrUpdate.tsx/UploadFile";
import { FC } from "react";
import { useSearchParams } from "react-router-dom";
import { determineCustomerKindColorValue, determineCustomerTypeColorValue } from "@/helpers/DetermineCustomerTypeColorValue";
import AdvisorName from "./AdveisorName";
import CustomerDefaultAddress from "./CustomerDefaultAddress";
import { useGetCustomerDetailsByType } from "@/apps/Admin/Pages/TempCustomer/ServerSideStates";
import { useTranslation } from "react-i18next";
import AutoDialerCalendar from "@/apps/Admin/Pages/Calendar/Components/Calendar/AutoDialerCalendar";

const GeneralInfoes: FC<{ mode: "add" | "edit" }> = ({ mode }) => {
  const {t}= useTranslation()
  const { Text } = Typography;
  const [searchParams] = useSearchParams()
  const customerId = searchParams.get("customerId")
   const customerDetails = useGetCustomerDetailsByType(
      customerId,
      searchParams.get("type") === "tempCustomer" ? "tempCustomer" : "customer"
    );
  const customerData = customerDetails?.data?.Value


  return (
    <>
      <Row
        gutter={[0, 10]}
        className=" !border-gray-100 !h-screen   !bg-gray-50 "
        style={{ borderRight: "1px solid gray" }}
      >
        <Col xs={24}>
          <Row gutter={[0, 10]} className="!px-2 py-2">
            
            <Col xs={24}>
              <div className="!flex items-center gap-2 ">
                
                <UploadImgFile />
                <div className="!flex flex-col gap-1 !w-full">
                  <div className="!flex items-center gap-1">
                    {customerData && (
                      <Text className="!text-blue-400">{customerData?.MainLanguageName}</Text>
                    )}
                    {customerData?.Classifications?.length>0 && (
                      <>
                      {
                        customerData?.Classifications?.map((item:any)=>{
                          return(
                          
                            <>
                          <Tag className="!bg-gray-300">{item.Name}</Tag>
                            </>
                          )
                        })
                      }
                      </>
                    )}
                  </div>
                  <div className="!flex gap-2 items-center">
                    <div className="!flex gap-1 items-center">
                     <div className={`w-[12px] !h-[12px] ${determineCustomerTypeColorValue("color",customerData?.Status)}`}></div>
                      <span className="!text-sm !font-bold">{`${customerData?.Name||""} ${customerData?.Surname||""}`}</span>
                    </div>
                   
                    
                  </div>
                  <div>
                  <span className="!text-sm !text-blue-400">{customerData?.Phone}</span>
                  </div>
                  <div className="!flex justify-between items-center !w-full">
                    <div className="!flex gap-2 items-center">
                      {customerData && (
                        <>
                        <Text className="text-xs "> {customerData?.Type===1?"Bireysel":"Kurumsal"} </Text>
                        <CustomerDefaultAddress/>
                        </>

                      )}
                      
                      
                    </div>
                   
                  </div>
                  {customerData&& (
                    <div>
                    
                      <Tag color={determineCustomerKindColorValue("color",customerData?.Kind,t)} className="!text-xs">
                       {determineCustomerKindColorValue("value",customerData?.Kind,t)}
                      </Tag>
                    </div>
                  )}
                  {customerData?.AdvisorId&& (
                    <div className="!flex gap-4 items-center">
                      <Tooltip title="Müşteri Temsilcisi">
                        <div className="!flex gap-1">
                         
                         <AdvisorName/>
                        </div>
                      </Tooltip>
                     
                    </div>
                  )}
                </div>
              </div>
            </Col>
            {/* {customerData && (
              <>
                <Col xs={24} className="!mt-6">
                <AutoDialerCalendar/>
                </Col>
                
              </>
            )} */}
          </Row>
        </Col>
      </Row>
    </>
  );
};

export default GeneralInfoes;
