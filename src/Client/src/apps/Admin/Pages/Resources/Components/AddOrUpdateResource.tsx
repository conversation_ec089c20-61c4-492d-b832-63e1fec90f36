import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row } from "antd";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { t } from "i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { createResource, updateResourceWithPut } from "../Services";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";

const AddOrUpdateResource: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);

  useEffect(() => {
    if (selectedRecord) {
      let data = {...selectedRecord}
      if(data["LanguageCode"]==="tr")
      {
        data["trValue"] = data["Value"]
      }
      else{
        data["enValue"] = data["Value"]
      }
      form.setFieldsValue({ ...data });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
  
    try {
      if (selectedRecord) {
        // güncelleme işlemi
        let data = {...selectedRecord}
        if(data["LanguageCode"]==="tr")
          {
            data["Value"] = formValues["trValue"]
          }
          else{
            data["Value"] = formValues["enValue"]
          }

        await updateResourceWithPut({ ...data});
      } else {
        // yeni kayıtlar hazırlanıyor
        let data: any[] = [
          {
            LanguageCode: "tr",
            Key: formValues["Key"],
            Value: formValues["trValue"],
          },
          {
            LanguageCode: "en",
            Key: formValues["Key"],
            Value: formValues["enValue"],
          },
        ];
  
        // Promise.all ile paralel çağrı
        await Promise.all(data.map(async (item) => await createResource(item)));
      }
  
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getResourceListFilter,
        exact: false,
      });
       queryClient.resetQueries({
        queryKey: endPoints.getResourceListByLangCode,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };
  
  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[20, 20]}>
          {
            selectedRecord&&
          <MazakaSelect
            label={t("resource.language")}
            placeholder={t("resource.language")}
            xs={24}
            name="LanguageCode"
            disabled={true}
            rules={[{ required: true, message: "" }]}
            options={[
              { label: "TR", value: "tr" },
              { label: "EN", value: "en" },
            ]}
          />
          }

          <MazakaInput
            label={t("resource.key")}
            placeholder={t("resource.key")}
            xs={24}
            name="Key"
            disabled={selectedRecord}
            rules={[{ required: true, message: "" }]}
          />
          {selectedRecord ? (
            <>
              {selectedRecord?.LanguageCode === "tr" ? (
                <>
                  <MazakaInput
                    label={t("resource.trValue")}
                    placeholder={t("resource.trValue")}
                    xs={24}
                    name="trValue"
                    rules={[{ required: true, message: "" }]}
                  />
                </>
              ) : (
                <>
                  <MazakaInput
                    label={t("resource.enValue")}
                    placeholder={t("resource.enValue")}
                    xs={24}
                    name="enValue"
                    rules={[{ required: true, message: "" }]}
                  />
                </>
              )}
            </>
          ) : (
            <>
            
            <MazakaInput
                    label={t("resource.trValue")}
                    placeholder={t("resource.trValue")}
                    xs={24}
                    name="trValue"
                    rules={[{ required: true, message: "" }]}
                  />
                  <MazakaInput
                    label={t("resource.enValue")}
                    placeholder={t("resource.enValue")}
                    xs={24}
                    name="enValue"
                    rules={[{ required: true, message: "" }]}
                  />
            </>
          )}

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("profession.edit") : t("profession.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateResource;
