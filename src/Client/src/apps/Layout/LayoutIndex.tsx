import { Layout } from "antd";
import { Content } from "antd/es/layout/layout";
import { FC, useEffect, useState } from "react";
import { Navigate, Outlet, useNavigate } from "react-router-dom";
import HeaderIndex from "./Components/Header/HeaderIndex";
import SidebarIndex from "./Components/Sidebar/SidebarIndex";
import { useDispatch, useSelector } from "react-redux";
import { getUserInfoes } from "../Account/Pages/Profile/Services";
import { handleSetProfileInfoes } from "../Account/Pages/Profile/ClientSideStates";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import GeneralPageLoading from "../Common/GeneralPageLoading";
import { RootState } from "@/store/Reducers";
import { useQueryClient } from "react-query";
import menuEndpoints from "@/apps/Layout/Components/Sidebar/EndPoints";
import { useGetAdminMenus } from "./Components/Sidebar/ServerSideStates";
import { useGetResourceByLang } from "../Admin/Pages/Resources/ServerSideStates";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

const LayoutIndex: FC<{ wrapperClassNames?: string }> = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const queryClient = useQueryClient();
  const userPermissions = useGetAdminMenus();
  const defaultLang = "tr";
  const initialLang = localStorage.getItem("lang") || defaultLang;
  // const[language,setLanguage] = useState(initialLang)
  // const resources = useGetResourceByLang(language?.toLowerCase());

  // useEffect(() => {
  //   if (resources?.data) {
  //     const resourceData = resources?.data?.Value?.Value || {};
  //     const formattedResourceData = resourceData.reduce(
  //       (acc: any, item: any) => {
  //         acc[item.Key] = item.Value;
  //         return acc;
  //       },
  //       {} as Record<string, any>
  //     );
  //     let finalResource = {
  //       TR: { translation: { ...formattedResourceData } },
  //       EN: { translation: {} },
  //     };

  //     i18n.use(initReactI18next).init({
  //       resources: finalResource,
  //       lng: language,
  //       fallbackLng: ["TR", "EN"],
  //       interpolation: {
  //         escapeValue: false,
  //       },
  //     });
  //   }
  // }, [resources.data]);

  useEffect(() => {
    const getUserInformations = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("access_token");
        if (token) {
          const response: any = await getUserInfoes();
          if (response?.Value) {
            dispatch(handleSetProfileInfoes({ data: response.Value }));
            queryClient.resetQueries({
              queryKey: menuEndpoints.getAdminMenus,
              exact: false,
            });
          }
        } else {
          localStorage.removeItem("access_token");
          navigate("/account/login");
        }
      } catch (error: any) {
        if (error?.status === 403) {
          navigate("/forbidden");
        } else if (error?.status === 401) {
          const errorMessage =
            error?.Message ||
            error?.Value?.Message ||
            "Kullanıcı Bilgileri çekerken bir hata oluştur";
          openNotificationWithIcon("error", errorMessage);

          localStorage.removeItem("access_token");
          navigate("/account/login");
        }
      } finally {
        setIsLoading(false);
      }
    };

    getUserInformations();
  }, [dispatch]);



// useEffect(() => {
//   const handleLanguageChange = (lng: string) => {
  
//   setLanguage(lng)
   
//   };

//   i18n.on("languageChanged", handleLanguageChange);

//   // Cleanup: component unmount olduğunda listener kaldır
//   return () => {
//     i18n.off("languageChanged", handleLanguageChange);
//   };
// }, []); 


  const isPageLoading = isLoading || userPermissions.isLoading

  if (isPageLoading) {
    return <GeneralPageLoading />;
  }

  if (!localStorage.getItem("access_token")) {
    return <Navigate to="/account/login" replace />;
  }
  return (
    <>
      <Layout className="">
        <HeaderIndex />
      </Layout>
      <Layout className="!bg-transparent">
        <SidebarIndex />
        <Content
          className={`${
            userInfoes?.Role?.toLowerCase() === "admin" ? "!ml-[70px]" : "!ml-0"
          } !mt-12 !bg-none !overflow-hidden`}
        >
          <Outlet />
        </Content>
      </Layout>
    </>
  );
};

export default LayoutIndex;
